"""
FSI 4.1 Currency Risk Calculator - Version 1
Calculates currency risk capital requirement per FSI 4.1 Section 7
"""

import pandas as pd
import numpy as np
import pyodbc
from openpyxl import load_workbook
from datetime import datetime
import os


class CurrencyRiskCalculator:
    """
    Calculator for FSI 4.1 Currency Risk Capital Requirement
    Section 7: Currency Risk
    """
    
    def __init__(self, access_db_path, excel_input_path):
        self.db_path = access_db_path
        self.excel_path = excel_input_path
        self.conn = None
        self.valuation_id = None
        
        # Standard currency shocks per FSI 4.1 Section 7.4
        self.standard_up_shock = 0.50  # 25% appreciation
        self.standard_down_shock = -0.30  # 25% depreciation
    
    # def connect_to_database(self):
    #     """Establish connection to Access database"""
    #     conn_str = (
    #         r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
    #         f'DBQ={self.db_path};'
    #     )
    #     self.conn = pyodbc.connect(conn_str)
    #     print(f"✓ Connected to database: {self.db_path}")
    #     return self.conn
    
    def load_excel_data(self):
        """Load all data from single input sheet"""
        print("Loading data from Excel...")
        
        wb = load_workbook(self.excel_path, data_only=True)
        ws = wb['Inputs']
        
        data = {}
        
        # Section 1: Valuation Identification
        data['valuation_id'] = ws['B6'].value
        data['valuation_date'] = ws['B7'].value
        data['quarter'] = ws['B8'].value
        data['year'] = ws['B9'].value
        
        # Section 2: Currency Positions (find dynamically)
        positions_data = []
        row = 14  # Start row for currency data
        while True:
            currency = ws[f'A{row}'].value
            if currency == 'TOTAL' or currency is None:
                break
            assets = ws[f'B{row}'].value
            liabilities = ws[f'C{row}'].value
            net_position = ws[f'D{row}'].value
            direction = ws[f'E{row}'].value
            exposure = ws[f'F{row}'].value
            
            if currency and (assets or liabilities):
                positions_data.append({
                    'Currency': currency,
                    'Assets': assets if assets else 0,
                    'Liabilities': liabilities if liabilities else 0,
                    'NetPosition': net_position if net_position else 0,
                    'Direction': direction,
                    'Exposure': exposure if exposure else 0
                })
            row += 1
        
        data['positions'] = pd.DataFrame(positions_data)
        
        # Section 3: Currency Shocks
        shocks_data = {}
        row_start = 28  # Approximate start of shocks section
        for i in range(10):  # Read up to 10 currencies
            row = row_start + i
            currency = ws[f'A{row}'].value
            up_shock = ws[f'B{row}'].value
            down_shock = ws[f'C{row}'].value
            
            if currency and (up_shock is not None or down_shock is not None):
                shocks_data[currency] = {
                    'up_shock': up_shock if up_shock is not None else self.standard_up_shock,
                    'down_shock': down_shock if down_shock is not None else self.standard_down_shock
                }
        
        data['shocks'] = shocks_data
        
        # Section 4: Exchange Rates (optional)
        # rate_data = []
        # try:
        #     row_start = 40  # Approximate start of rates section
        #     for i in range(5):
        #         row = row_start + i
        #         currency_pair = ws[f'A{row}'].value
        #         spot_rate = ws[f'B{row}'].value
                
        #         if currency_pair and spot_rate:
        #             rate_data.append({
        #                 'CurrencyPair': currency_pair,
        #                 'SpotRate': spot_rate
        #             })
        # except:
        #     pass
        
        # data['rates'] = pd.DataFrame(rate_data) if rate_data else None
        
        self.valuation_id = int(data['valuation_id'])
        
        print(f"✓ Loaded data for Valuation ID: {self.valuation_id}")
        print(f"✓ Loaded {len(data['positions'])} currency positions")
        print(f"✓ Loaded shocks for {len(data['shocks'])} currencies")
        
        wb.close()
        
        return data
    
    def validate_positions(self, positions_df):
        """Validate currency positions data"""
        
        if positions_df.empty:
            raise ValueError("No currency positions provided")
        
        # Check for required columns
        required_cols = ['Currency', 'Assets', 'Liabilities', 'NetPosition']
        for col in required_cols:
            if col not in positions_df.columns:
                raise ValueError(f"Missing required column: {col}")
        
        # Check for numeric values
        for idx, row in positions_df.iterrows():
            if not isinstance(row['Assets'], (int, float)):
                print(f"Warning: Non-numeric assets for {row['Currency']}")
            if not isinstance(row['Liabilities'], (int, float)):
                print(f"Warning: Non-numeric liabilities for {row['Currency']}")
        
        print(f"✓ Position validation passed")
        return True
    
    # def save_inputs_to_database(self, data):
    #     """Save input data to database"""
    #     cursor = self.conn.cursor()
        
    #     try:
    #         # Check if valuation period exists
    #         cursor.execute("""
    #             SELECT COUNT(*) FROM ValuationPeriods WHERE ValuationID = ?
    #         """, (self.valuation_id,))
            
    #         if cursor.fetchone()[0] == 0:
    #             cursor.execute("""
    #                 INSERT INTO ValuationPeriods 
    #                 (ValuationID, ValuationDate, Quarter, Year, Description)
    #                 VALUES (?, ?, ?, ?, ?)
    #             """, (
    #                 self.valuation_id,
    #                 data['valuation_date'],
    #                 data['quarter'],
    #                 data['year'],
    #                 f"{data['quarter']} {data['year']} Currency Risk"
    #             ))
            
    #         # Delete existing currency risk inputs
    #         cursor.execute("""
    #             DELETE FROM Input_CurrencyPositions WHERE ValuationID = ?
    #         """, (self.valuation_id,))
            
    #         # Insert currency positions
    #         for _, row in data['positions'].iterrows():
    #             cursor.execute("""
    #                 INSERT INTO Input_CurrencyPositions
    #                 (ValuationID, Currency, Assets, Liabilities, NetPosition)
    #                 VALUES (?, ?, ?, ?, ?)
    #             """, (
    #                 self.valuation_id,
    #                 row['Currency'],
    #                 row['Assets'],
    #                 row['Liabilities'],
    #                 row['NetPosition']
    #             ))
            
    #         self.conn.commit()
    #         print(f"✓ Saved inputs to database for Valuation ID: {self.valuation_id}")
            
    #     except Exception as e:
    #         self.conn.rollback()
    #         print(f"✗ Error saving inputs to database: {str(e)}")
    #         # Continue anyway for non-database runs
    
    def calculate_currency_risk(self, positions_df, shocks):
        """
        Calculate currency risk per FSI 4.1 Section 7
        """
        
        results = []
        
        print("\n" + "="*70)
        print("CURRENCY RISK CALCULATION BY CURRENCY")
        print("="*70)
        
        for _, position in positions_df.iterrows():
            currency = position['Currency']
            net_position = position['NetPosition']
            
            # Skip if net position is zero
            if abs(net_position) < 0.01:
                print(f"\n{currency}: Zero net position - No risk")
                continue
            
            # Get shocks for this currency (default to standard if not specified)
            if currency in shocks:
                up_shock = shocks[currency]['up_shock']
                down_shock = shocks[currency]['down_shock']
            else:
                up_shock = self.standard_up_shock
                down_shock = self.standard_down_shock
            
            # Calculate losses under each scenario
            # Up-shock: Currency appreciates 25%
            up_loss = net_position * up_shock
            
            # Down-shock: Currency depreciates 25%
            down_loss = net_position * down_shock
            
            # Capital requirement is the maximum loss
            # For long position: down-shock is worse (negative)
            # For short position: up-shock is worse (positive becomes more negative)
            if net_position > 0:  # Long position
                # Down-shock causes loss
                capital_req = abs(down_loss)
            else:  # Short position
                # Up-shock causes loss
                capital_req = abs(up_loss)
            
            results.append({
                'currency': currency,
                'net_position': net_position,
                'direction': 'Long' if net_position > 0 else 'Short',
                'up_shock': up_shock,
                'down_shock': down_shock,
                'up_loss': up_loss,
                'down_loss': down_loss,
                'capital_requirement': capital_req
            })
            
            print(f"\n{currency}:")
            print(f"  Net Position:        R {net_position:,.2f} ({results[-1]['direction']})")
            print(f"  Up-Shock (+{up_shock*100:.1f}%):  R {up_loss:,.2f}")
            print(f"  Down-Shock ({down_shock*100:.1f}%): R {down_loss:,.2f}")
            print(f"  Capital Requirement: R {capital_req:,.2f}")
        
        print("="*70 + "\n")
        
        return pd.DataFrame(results)
    
    def aggregate_currency_risk(self, results_df):
        """
        Aggregate currency risk across all currencies
        Per FSI 4.1: Simple sum (no diversification benefit)
        """
        
        total_up_losses = results_df['up_loss'].sum()
        total_down_losses = results_df['down_loss'].sum()
        total_capital_req = results_df['capital_requirement'].sum()
        
        # Maximum loss across all currencies
        max_loss = max(abs(total_up_losses), abs(total_down_losses))
        
        print("\n" + "="*70)
        print("AGGREGATED CURRENCY RISK")
        print("="*70)
        print(f"Total Up Losses:      R {total_up_losses:,.2f}")
        print(f"Total Down Losses:    R {total_down_losses:,.2f}")
        print(f"Maximum Loss:         R {max_loss:,.2f}")
        print(f"Total Capital Req:    R {total_capital_req:,.2f}")
        print("\nNote: No diversification benefit - simple sum per FSI 4.1 Section 7")
        print("="*70 + "\n")
        
        return {
            'total_up_losses': total_up_losses,
            'total_down_losses': total_down_losses,
            'max_loss': max_loss,
            'total_capital_requirement': total_capital_req
        }
    
    # def save_results_to_database(self, results_df, aggregated):
    #     """Save results to database"""
    #     cursor = self.conn.cursor()
        
    #     try:
    #         # Delete existing results
    #         cursor.execute("""
    #             DELETE FROM Output_CurrencyRiskByFX WHERE ValuationID = ?
    #         """, (self.valuation_id,))
    #         cursor.execute("""
    #             DELETE FROM Output_CurrencyRiskAggregated WHERE ValuationID = ?
    #         """, (self.valuation_id,))
            
    #         # Insert results by currency
    #         for _, result in results_df.iterrows():
    #             cursor.execute("""
    #                 INSERT INTO Output_CurrencyRiskByFX
    #                 (ValuationID, Currency, NetPosition, UpShock, DownShock,
    #                  UpLoss, DownLoss, CapitalRequirement)
    #                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    #             """, (
    #                 self.valuation_id,
    #                 result['currency'],
    #                 result['net_position'],
    #                 result['up_shock'],
    #                 result['down_shock'],
    #                 result['up_loss'],
    #                 result['down_loss'],
    #                 result['capital_requirement']
    #             ))
            
    #         # Insert aggregated results
    #         cursor.execute("""
    #             INSERT INTO Output_CurrencyRiskAggregated
    #             (ValuationID, TotalUpLosses, TotalDownLosses, MaximumLoss,
    #              TotalCapitalReq, CalculationMethod)
    #             VALUES (?, ?, ?, ?, ?, ?)
    #         """, (
    #             self.valuation_id,
    #             aggregated['total_up_losses'],
    #             aggregated['total_down_losses'],
    #             aggregated['max_loss'],
    #             aggregated['total_capital_requirement'],
    #             'FSI 4.1 Section 7 - Simple sum'
    #         ))
            
    #         self.conn.commit()
    #         print(f"✓ Saved results to database for Valuation ID: {self.valuation_id}")
            
    #     except Exception as e:
    #         self.conn.rollback()
    #         print(f"✗ Error saving results: {str(e)}")
    #         # Continue anyway
    
    def export_results_to_excel(self, results_df, aggregated):
        """Export results to Outputs sheet"""
        
        wb = load_workbook(self.excel_path)
        ws = wb['Outputs']
        
        # Update timestamp
        ws['B5'] = datetime.now()
        ws['B5'].number_format = 'yyyy-mm-dd hh:mm:ss'
        ws['D5'] = "Success"
        ws['D5'].font = Font(color="008000", bold=True)
        
        # Results by currency (rows 10 onwards)
        for idx, (_, result) in enumerate(results_df.iterrows(), start=10):
            ws.cell(row=idx, column=1, value=result['currency'])
            ws.cell(row=idx, column=2, value=result['net_position'])
            ws.cell(row=idx, column=3, value=result['direction'])
            ws.cell(row=idx, column=4, value=result['up_shock'])
            ws.cell(row=idx, column=5, value=result['down_shock'])
            ws.cell(row=idx, column=6, value=result['up_loss'])
            ws.cell(row=idx, column=7, value=result['down_loss'])
            ws.cell(row=idx, column=8, value=result['capital_requirement'])
        
        # Aggregated results (rows 24-27)
        ws.cell(row=24, column=2, value=aggregated['total_up_losses'])
        ws.cell(row=25, column=2, value=aggregated['total_down_losses'])
        ws.cell(row=26, column=2, value=aggregated['max_loss'])
        ws.cell(row=27, column=2, value=aggregated['total_capital_requirement'])
        
        # Save
        output_path = self.excel_path.replace('.xlsx', '_Results.xlsx')
        wb.save(output_path)
        print(f"✓ Results exported to: {output_path}")
        
        return output_path
    
    # def log_calculation(self, run_start, run_end, status):
    #     """Log calculation to database"""
    #     cursor = self.conn.cursor()
        
    #     try:
    #         duration = int((run_end - run_start).total_seconds())
            
    #         cursor.execute("""
    #             INSERT INTO CalculationHistory
    #             (ValuationID, RunStartTime, RunEndTime, RunDuration, RunStatus,
    #              InputFileLocation, RunBy)
    #             VALUES (?, ?, ?, ?, ?, ?, ?)
    #         """, (
    #             self.valuation_id,
    #             run_start,
    #             run_end,
    #             duration,
    #             status,
    #             self.excel_path,
    #             os.getlogin()
    #         ))
            
    #         self.conn.commit()
            
    #     except Exception as e:
    #         print(f"Warning: Could not log calculation: {str(e)}")
    
    def run_full_calculation(self):
        """Execute complete currency risk calculation"""
        
        run_start = datetime.now()
        
        try:
            print("\n" + "="*70)
            print("FSI 4.1 CURRENCY RISK CALCULATION")
            print("="*70 + "\n")
            
            # 1. Load data
            data = self.load_excel_data()
            
            # 2. Validate
            self.validate_positions(data['positions'])
            
            # # 3. Connect to database
            # try:
            #     self.connect_to_database()
            # except:
            #     print("Warning: Could not connect to database. Continuing without database storage.")
            #     self.conn = None
            
            # # 4. Save inputs (if database available)
            # if self.conn:
            #     self.save_inputs_to_database(data)
            
            # 5. Calculate currency risk by currency
            results_df = self.calculate_currency_risk(data['positions'], data['shocks'])
            
            # 6. Aggregate
            aggregated = self.aggregate_currency_risk(results_df)
            
            # 7. Save results (if database available)
            if self.conn:
                self.save_results_to_database(results_df, aggregated)
            
            # 8. Export to Excel
            output_file = self.export_results_to_excel(results_df, aggregated)
            
            # 9. Log (if database available)
            run_end = datetime.now()
            if self.conn:
                self.log_calculation(run_start, run_end, 'Success')
            
            print("\n" + "="*70)
            print("CALCULATION COMPLETE")
            print("="*70)
            print(f"Valuation ID:        {self.valuation_id}")
            print(f"Total Capital Req:   R {aggregated['total_capital_requirement']:,.2f}")
            print(f"Output File:         {output_file}")
            print(f"Run Time:            {(run_end - run_start).total_seconds():.2f} seconds")
            print("="*70 + "\n")
            
            return {
                'valuation_id': self.valuation_id,
                'results': results_df,
                'aggregated': aggregated,
                'output_file': output_file
            }
            
        except Exception as e:
            run_end = datetime.now()
            if self.conn:
                self.log_calculation(run_start, run_end, 'Failed')
            print(f"\n✗ Calculation failed: {str(e)}")
            raise
        
        finally:
            if self.conn:
                self.conn.close()


# ============================================================================
# MAIN EXECUTION
# ============================================================================

if __name__ == "__main__":
    
    # Configuration
    DB_PATH = r"C:\MarketRisk\MarketRisk_Database.accdb"
    EXCEL_INPUT = r"C:\Users\<USER>\OneDrive\Documents\SCR\Equity Risk\CurrencyRisk_Input_Template_v1.xlsx"
    
    # Initialize calculator
    calculator = CurrencyRiskCalculator(DB_PATH, EXCEL_INPUT)
    
    # Run calculation
    try:
        results = calculator.run_full_calculation()
        print("\n✓ Currency risk calculation completed successfully!")
        
    except Exception as e:
        print(f"\n✗ Calculation failed with error:")
        print(f"  {str(e)}")
        import traceback
        traceback.print_exc()

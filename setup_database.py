"""
Access Database Setup Script for FSI 4.1 Equity Risk
Creates all tables and populates reference data
"""

import pyodbc
from datetime import datetime
import os


def create_access_database(db_path):
    """
    Create and initialize Access database for Equity Risk calculations
    """
    
    print("="*70)
    print("FSI 4.1 EQUITY RISK DATABASE SETUP")
    print("="*70 + "\n")
    
    # Check if database already exists
    if os.path.exists(db_path):
        response = input(f"Database already exists at {db_path}. Overwrite? (y/n): ")
        if response.lower() != 'y':
            print("Setup cancelled.")
            return
        os.remove(db_path)
        print(f"✓ Removed existing database\n")
    
    # Create new database
    print(f"Creating new database at: {db_path}")
    
    # Connection string for creating new database
    conn_str = (
        r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
        f'DBQ={db_path};'
    )
    
    # Create database file
    pyodbc.connect(
        r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
        f'DBQ={db_path};'
        'PWD=;'
    )
    
    print(f"✓ Database created successfully\n")
    
    # Connect to the new database
    conn = pyodbc.connect(conn_str)
    cursor = conn.cursor()
    
    print("Creating tables...\n")
    
    # ========================================================================
    # REFERENCE TABLES
    # ========================================================================
    
    # ValuationPeriods
    cursor.execute("""
        CREATE TABLE ValuationPeriods (
            ValuationID INTEGER PRIMARY KEY,
            ValuationDate DATETIME NOT NULL,
            Quarter TEXT(2),
            Year INTEGER,
            Description TEXT(100),
            CreatedDate DATETIME,
            CreatedBy TEXT(50),
            Status TEXT(20)
        )
    """)
    print("✓ Created table: ValuationPeriods")
    
    # EquityChargeGroups
    cursor.execute("""
        CREATE TABLE EquityChargeGroups (
            ChargeGroupID AUTOINCREMENT PRIMARY KEY,
            ChargeGroupName TEXT(20) NOT NULL,
            BaseShock DOUBLE NOT NULL,
            BiParameter DOUBLE NOT NULL,
            IndexName TEXT(100),
            Description TEXT(255)
        )
    """)
    print("✓ Created table: EquityChargeGroups")
    
    # ========================================================================
    # INPUT TABLES
    # ========================================================================
    
    # Input_BalanceSheet
    cursor.execute("""
        CREATE TABLE Input_BalanceSheet (
            InputID AUTOINCREMENT PRIMARY KEY,
            ValuationID INTEGER NOT NULL,
            TotalEquityValue CURRENCY NOT NULL,
            TotalAssets CURRENCY NOT NULL,
            BasicOwnFunds CURRENCY NOT NULL,
            BestEstimateLiabilities CURRENCY NOT NULL,
            InputDate DATETIME,
            InputBy TEXT(50)
        )
    """)
    print("✓ Created table: Input_BalanceSheet")
    
    # Input_EquityAllocation
    cursor.execute("""
        CREATE TABLE Input_EquityAllocation (
            AllocationID AUTOINCREMENT PRIMARY KEY,
            ValuationID INTEGER NOT NULL,
            EquityClass TEXT(50) NOT NULL,
            AllocationPercent DOUBLE NOT NULL,
            ChargeGroup TEXT(20) NOT NULL,
            CalculatedValue CURRENCY,
            Notes TEXT(255)
        )
    """)
    print("✓ Created table: Input_EquityAllocation")
    
    # Input_IndexValues
    cursor.execute("""
        CREATE TABLE Input_IndexValues (
            IndexInputID AUTOINCREMENT PRIMARY KEY,
            ValuationID INTEGER NOT NULL,
            ValuationDate DATETIME NOT NULL,
            GlobalIndexCurrent DOUBLE,
            GlobalIndex3YMA DOUBLE,
            SAIndexCurrent DOUBLE,
            SAIndex3YMA DOUBLE,
            DataSource TEXT(50),
            InputDate DATETIME
        )
    """)
    print("✓ Created table: Input_IndexValues")
    
    # Calc_SymmetricAdjustments
    cursor.execute("""
        CREATE TABLE Calc_SymmetricAdjustments (
            AdjustmentID AUTOINCREMENT PRIMARY KEY,
            ValuationID INTEGER NOT NULL,
            ChargeGroup TEXT(20) NOT NULL,
            CurrentMARate DOUBLE,
            SymmetricAdjustment DOUBLE,
            TotalShock DOUBLE,
            CalculationDate DATETIME
        )
    """)
    print("✓ Created table: Calc_SymmetricAdjustments")
    
    # ========================================================================
    # OUTPUT TABLES
    # ========================================================================
    
    # Output_EquityRiskByGroup
    cursor.execute("""
        CREATE TABLE Output_EquityRiskByGroup (
            ResultID AUTOINCREMENT PRIMARY KEY,
            ValuationID INTEGER NOT NULL,
            ChargeGroup TEXT(20) NOT NULL,
            TotalShockApplied DOUBLE,
            ValuePreShock CURRENCY,
            ValuePostShock CURRENCY,
            ChangeInValue CURRENCY,
            CapitalRequirement CURRENCY,
            CalculationDate DATETIME
        )
    """)
    print("✓ Created table: Output_EquityRiskByGroup")
    
    # Output_EquityRiskAggregated
    cursor.execute("""
        CREATE TABLE Output_EquityRiskAggregated (
            AggResultID AUTOINCREMENT PRIMARY KEY,
            ValuationID INTEGER NOT NULL,
            GlobalCapitalReq CURRENCY,
            SACapitalReq CURRENCY,
            OtherCapitalReq CURRENCY,
            TotalCapitalReq CURRENCY,
            CorrelationBenefit CURRENCY,
            CalculationDate DATETIME,
            CalculationMethod TEXT(50)
        )
    """)
    print("✓ Created table: Output_EquityRiskAggregated")
    
    # Output_CalculationLog
    cursor.execute("""
        CREATE TABLE Output_CalculationLog (
            LogID AUTOINCREMENT PRIMARY KEY,
            ValuationID INTEGER NOT NULL,
            CalculationStep TEXT(100),
            StepResult TEXT(255),
            CalculationTimestamp DATETIME,
            ErrorFlag YESNO,
            ErrorMessage MEMO
        )
    """)
    print("✓ Created table: Output_CalculationLog")
    
    # ========================================================================
    # AUDIT TABLES
    # ========================================================================
    
    # AuditTrail
    cursor.execute("""
        CREATE TABLE AuditTrail (
            AuditID AUTOINCREMENT PRIMARY KEY,
            ValuationID INTEGER,
            TableName TEXT(50),
            RecordID INTEGER,
            ActionType TEXT(20),
            OldValue MEMO,
            NewValue MEMO,
            ChangedBy TEXT(50),
            ChangedDate DATETIME
        )
    """)
    print("✓ Created table: AuditTrail")
    
    # CalculationHistory
    cursor.execute("""
        CREATE TABLE CalculationHistory (
            RunID AUTOINCREMENT PRIMARY KEY,
            ValuationID INTEGER NOT NULL,
            RunStartTime DATETIME,
            RunEndTime DATETIME,
            RunDuration INTEGER,
            RunStatus TEXT(20),
            InputFileLocation TEXT(255),
            OutputFileLocation TEXT(255),
            RunBy TEXT(50)
        )
    """)
    print("✓ Created table: CalculationHistory")
    
    conn.commit()
    
    # ========================================================================
    # POPULATE REFERENCE DATA
    # ========================================================================
    
    print("\nPopulating reference data...\n")
    
    # Insert Equity Charge Groups
    charge_groups = [
        ('Global', 0.39, 0.08, 'MSCI World Developed Markets Price Index', 
         'Equities in EEA/OECD markets'),
        ('SA', 0.43, 0.15, 'JSE Allshare Equity Price Index', 
         'JSE listed equities'),
        ('Other', 0.49, 0.15, 'JSE Allshare Equity Price Index', 
         'Other equities incl. emerging markets')
    ]
    
    for cg in charge_groups:
        cursor.execute("""
            INSERT INTO EquityChargeGroups 
            (ChargeGroupName, BaseShock, BiParameter, IndexName, Description)
            VALUES (?, ?, ?, ?, ?)
        """, cg)
    
    print("✓ Populated EquityChargeGroups")
    
    # Insert sample valuation periods
    sample_periods = [
        (418, datetime(2023, 12, 31), 'Q4', 2023, '2023 Q4 Valuation'),
        (419, datetime(2024, 3, 31), 'Q1', 2024, '2024 Q1 Valuation'),
        (420, datetime(2024, 6, 30), 'Q2', 2024, '2024 Q2 Valuation'),
    ]
    
    for period in sample_periods:
        cursor.execute("""
            INSERT INTO ValuationPeriods 
            (ValuationID, ValuationDate, Quarter, Year, Description, 
             CreatedDate, Status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (*period, datetime.now(), 'Active'))
    
    print("✓ Populated sample ValuationPeriods")
    
    conn.commit()
    
    # ========================================================================
    # CREATE INDEXES
    # ========================================================================
    
    print("\nCreating indexes...\n")
    
    indexes = [
        ("idx_ValPeriods_Date", "ValuationPeriods", "ValuationDate"),
        ("idx_ValPeriods_Status", "ValuationPeriods", "Status"),
        ("idx_InputBS_ValID", "Input_BalanceSheet", "ValuationID"),
        ("idx_InputAlloc_ValID", "Input_EquityAllocation", "ValuationID"),
        ("idx_InputIndex_ValID", "Input_IndexValues", "ValuationID"),
        ("idx_OutputByGroup_ValID", "Output_EquityRiskByGroup", "ValuationID"),
        ("idx_OutputAgg_ValID", "Output_EquityRiskAggregated", "ValuationID"),
        ("idx_CalcLog_ValID", "Output_CalculationLog", "ValuationID"),
        ("idx_CalcHist_ValID", "CalculationHistory", "ValuationID"),
    ]
    
    for idx_name, table, column in indexes:
        try:
            cursor.execute(f"""
                CREATE INDEX {idx_name} ON {table}({column})
            """)
            print(f"✓ Created index: {idx_name}")
        except:
            pass  # Index might already exist
    
    conn.commit()
    
    # ========================================================================
    # VERIFY SETUP
    # ========================================================================
    
    print("\nVerifying database setup...\n")
    
    # Count tables
    cursor.execute("""
        SELECT COUNT(*) 
        FROM MSysObjects 
        WHERE Type=1 AND Flags=0
    """)
    table_count = cursor.fetchone()[0]
    print(f"✓ Total tables created: {table_count}")
    
    # Count charge groups
    cursor.execute("SELECT COUNT(*) FROM EquityChargeGroups")
    cg_count = cursor.fetchone()[0]
    print(f"✓ Equity charge groups: {cg_count}")
    
    # Count valuation periods
    cursor.execute("SELECT COUNT(*) FROM ValuationPeriods")
    vp_count = cursor.fetchone()[0]
    print(f"✓ Sample valuation periods: {vp_count}")
    
    conn.close()
    
    print("\n" + "="*70)
    print("DATABASE SETUP COMPLETE!")
    print("="*70)
    print(f"\nDatabase location: {db_path}")
    print("\nYou can now:")
    print("1. Open the database in Microsoft Access")
    print("2. Run equity risk calculations using the Python script")
    print("3. View historical results and audit trails")
    print("\n")


def display_database_info(db_path):
    """Display information about existing database"""
    
    if not os.path.exists(db_path):
        print(f"Database not found at: {db_path}")
        return
    
    conn_str = (
        r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
        f'DBQ={db_path};'
    )
    
    conn = pyodbc.connect(conn_str)
    cursor = conn.cursor()
    
    print("\n" + "="*70)
    print("DATABASE INFORMATION")
    print("="*70 + "\n")
    
    print(f"Location: {db_path}")
    print(f"Size: {os.path.getsize(db_path) / 1024:.2f} KB\n")
    
    # Valuation periods
    cursor.execute("""
        SELECT ValuationID, ValuationDate, Quarter, Year, Description
        FROM ValuationPeriods
        ORDER BY ValuationDate DESC
    """)
    
    periods = cursor.fetchall()
    if periods:
        print("Valuation Periods:")
        print("-" * 70)
        for p in periods:
            print(f"  {p[0]} | {p[1].strftime('%Y-%m-%d')} | {p[2]} {p[3]} | {p[4]}")
    
    # Recent calculations
    cursor.execute("""
        SELECT TOP 5 
            ValuationID, 
            TotalCapitalReq, 
            CalculationDate
        FROM Output_EquityRiskAggregated
        ORDER BY CalculationDate DESC
    """)
    
    results = cursor.fetchall()
    if results:
        print("\nRecent Calculations:")
        print("-" * 70)
        for r in results:
            print(f"  Val ID {r[0]} | Capital Req: R {r[1]:,.2f} | {r[2].strftime('%Y-%m-%d %H:%M')}")
    
    conn.close()
    print("\n")


if __name__ == "__main__":
    
    import sys
    
    # Default database path
    DB_PATH = r"C:\Users\<USER>\OneDrive\Documents\SCR\Equity Risk\EquityRisk_Database.accdb"
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'info':
            display_database_info(DB_PATH)
        elif sys.argv[1] == 'create':
            create_access_database(DB_PATH)
        else:
            print("Usage:")
            print("  python setup_database.py create  - Create new database")
            print("  python setup_database.py info    - Display database info")
    else:
        # Interactive mode
        print("\nFSI 4.1 Equity Risk Database Setup")
        print("1. Create new database")
        print("2. Display database information")
        print("3. Exit")
        
        choice = input("\nSelect option (1-3): ")
        
        if choice == '1':
            create_access_database(DB_PATH)
        elif choice == '2':
            display_database_info(DB_PATH)
        else:
            print("Exiting...")

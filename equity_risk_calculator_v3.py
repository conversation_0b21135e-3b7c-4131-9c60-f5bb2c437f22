"""
FSI 4.1 Equity Risk Calculator - Version 3
All inputs on one sheet, symmetric adjustments provided by user
"""

import pandas as pd
import numpy as np
import pyodbc
from openpyxl import load_workbook
from openpyxl.styles import Font
from datetime import datetime
import os


class EquityRiskCalculator:
    """
    Calculator for FSI 4.1 Equity Risk Capital Requirement
    Version 3: Symmetric adjustments provided as inputs
    """
    
    def __init__(self, access_db_path, excel_input_path):
        self.db_path = access_db_path
        self.excel_path = excel_input_path
        self.conn = None
        self.valuation_id = None
        
        # Correlation matrix (Section 6.9)
        self.correlation_matrix = pd.DataFrame(
            [[1.00, 0.75, 0.75],
             [0.75, 1.00, 0.75],
             [0.75, 0.75, 1.00]],
            index=['Global', 'SA', 'Other'],
            columns=['Global', 'SA', 'Other']
        )
    
    def connect_to_database(self):
        """Establish connection to Access database"""
        conn_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            f'DBQ={self.db_path};'
        )
        self.conn = pyodbc.connect(conn_str)
        print(f"✓ Connected to database: {self.db_path}")
        return self.conn
    
    def load_excel_data(self):
        """Load all data from single input sheet"""
        print("Loading data from Excel...")
        
        wb = load_workbook(self.excel_path, data_only=True)
        ws = wb['Inputs']
        
        # Extract data from specific sections
        data = {}
        
        # Section 1: Valuation Identification (rows 6-9)
        data['valuation_id'] = ws['B6'].value
        data['valuation_date'] = ws['B7'].value
        data['quarter'] = ws['B8'].value
        # Extract year from the datetime in B7 or set manually
        if hasattr(data['valuation_date'], 'year'):
            data['year'] = data['valuation_date'].year
        else:
            data['year'] = 2024  # Default fallback
        
        # Section 2: Balance Sheet Data (rows 13-16)
        data['total_equity_value'] = ws['B13'].value
        data['total_assets'] = ws['B14'].value
        data['basic_own_funds'] = ws['B15'].value
        data['best_estimate_liabilities'] = ws['B16'].value
        
        # Section 3: Equity Allocation (find dynamically)
        # Find the allocation table
        allocation_data = []
        row = 21  # Start row for allocation data
        while True:
            equity_class = ws[f'A{row}'].value
            if equity_class == 'TOTAL' or equity_class is None:
                break
            allocation_pct = ws[f'B{row}'].value
            charge_group = ws[f'C{row}'].value
            calculated_value = ws[f'D{row}'].value
            
            if equity_class and allocation_pct:
                # Calculate the value if it's missing
                if calculated_value is None:
                    calculated_value = allocation_pct * data['total_equity_value']

                allocation_data.append({
                    'Class': equity_class,
                    'Allocation': allocation_pct,
                    'ChargeGroup': charge_group,
                    'CalculatedValue': calculated_value
                })
            row += 1
        
        data['allocation'] = pd.DataFrame(allocation_data)
        
        # Section 4: Equity Shocks (find dynamically)
        # Find shocks table
        shocks_data = {}
        row_start = 35  # Approximate start of shocks section
        for i in range(3):  # Global, SA, Other
            row = row_start + i
            charge_group = ws[f'A{row}'].value
            base_shock = ws[f'B{row}'].value
            symmetric_adj = ws[f'C{row}'].value
            total_shock = ws[f'D{row}'].value
            
            if charge_group:
                # Calculate total_shock if it's missing
                if total_shock is None and base_shock is not None and symmetric_adj is not None:
                    total_shock = base_shock + symmetric_adj

                shocks_data[charge_group] = {
                    'base_shock': base_shock,
                    'symmetric_adjustment': symmetric_adj,
                    'total_shock': total_shock
                }
        
        data['shocks'] = shocks_data
        
        # Section 5: Index Values (optional reference)
        index_data = []
        try:
            row_start = 43  # Approximate start of index section
            for i in range(2):
                row = row_start + i
                index_name = ws[f'A{row}'].value
                current_value = ws[f'B{row}'].value
                three_yr_ma = ws[f'C{row}'].value
                data_source = ws[f'D{row}'].value
                
                if index_name:
                    index_data.append({
                        'Index': index_name,
                        'CurrentValue': current_value,
                        'ThreeYearMA': three_yr_ma,
                        'DataSource': data_source
                    })
        except:
            pass  # Index data is optional
        
        data['indices'] = pd.DataFrame(index_data) if index_data else None
        
        self.valuation_id = int(data['valuation_id'])
        
        print(f"✓ Loaded data for Valuation ID: {self.valuation_id}")
        print(f"✓ Loaded {len(data['allocation'])} allocation classes")
        print(f"✓ Loaded shocks for {len(data['shocks'])} charge groups")
        
        wb.close()
        
        return data
    
    def validate_allocation(self, allocation_df):
        """Validate that allocation percentages sum to 100%"""
        total_alloc = allocation_df['Allocation'].sum()
        
        if abs(total_alloc - 1.0) > 0.0001:
            raise ValueError(
                f"Allocation percentages must sum to 100%. Current total: {total_alloc*100:.2f}%"
            )
        
        print(f"✓ Allocation validation passed: {total_alloc*100:.1f}%")
        return True
    
    def validate_shocks(self, shocks):
        """Validate shock data"""
        required_groups = ['Global', 'SA', 'Other']
        
        for group in required_groups:
            if group not in shocks:
                raise ValueError(f"Missing shock data for charge group: {group}")
            
            if shocks[group]['symmetric_adjustment'] is None:
                raise ValueError(f"Symmetric adjustment not provided for {group}")

            if shocks[group]['base_shock'] is None:
                raise ValueError(f"Base shock not provided for {group}")

            if shocks[group]['total_shock'] is None:
                raise ValueError(f"Total shock not provided for {group}")

            # Validate total shock calculation
            expected_total = shocks[group]['base_shock'] + shocks[group]['symmetric_adjustment']
            actual_total = shocks[group]['total_shock']

            if abs(expected_total - actual_total) > 0.0001:
                print(f"Warning: Total shock for {group} may be incorrect. " + 
                      f"Expected: {expected_total*100:.2f}%, Got: {actual_total*100:.2f}%")
        
        print(f"✓ Shock validation passed")
        return True
    
    def save_inputs_to_database(self, data):
        """Save input data to database"""
        cursor = self.conn.cursor()
        
        try:
            # Check if valuation period exists
            cursor.execute("""
                SELECT COUNT(*) FROM ValuationPeriods WHERE ValuationID = ?
            """, (self.valuation_id,))
            
            if cursor.fetchone()[0] == 0:
                cursor.execute("""
                    INSERT INTO ValuationPeriods 
                    (ValuationID, ValuationDate, Quarter, Year, Description)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    self.valuation_id,
                    data['valuation_date'],
                    data['quarter'],
                    data['year'],
                    f"{data['quarter']} {data['year']} Valuation"
                ))
                print(f"✓ Created valuation period: {self.valuation_id}")
            
            # Delete existing inputs
            cursor.execute("DELETE FROM Input_BalanceSheet WHERE ValuationID = ?", 
                          (self.valuation_id,))
            cursor.execute("DELETE FROM Input_EquityAllocation WHERE ValuationID = ?", 
                          (self.valuation_id,))
            cursor.execute("DELETE FROM Calc_SymmetricAdjustments WHERE ValuationID = ?", 
                          (self.valuation_id,))
            
            # Insert balance sheet data
            cursor.execute("""
                INSERT INTO Input_BalanceSheet 
                (ValuationID, TotalEquityValue, TotalAssets, BasicOwnFunds, BestEstimateLiabilities)
                VALUES (?, ?, ?, ?, ?)
            """, (
                self.valuation_id,
                data['total_equity_value'],
                data['total_assets'],
                data['basic_own_funds'],
                data['best_estimate_liabilities']
            ))
            
            # Insert equity allocation
            for _, row in data['allocation'].iterrows():
                cursor.execute("""
                    INSERT INTO Input_EquityAllocation
                    (ValuationID, EquityClass, AllocationPercent, ChargeGroup, CalculatedValue)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    self.valuation_id,
                    row['Class'],
                    row['Allocation'],
                    row['ChargeGroup'],
                    row['CalculatedValue']
                ))
            
            # Insert symmetric adjustments (as provided by user)
            for charge_group, shock_info in data['shocks'].items():
                cursor.execute("""
                    INSERT INTO Calc_SymmetricAdjustments
                    (ValuationID, ChargeGroup, CurrentMARate, SymmetricAdjustment, TotalShock)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    self.valuation_id,
                    charge_group,
                    None,  # Not calculated, so no current/MA ratio
                    shock_info['symmetric_adjustment'],
                    shock_info['total_shock']
                ))
            
            self.conn.commit()
            print(f"✓ Saved inputs to database for Valuation ID: {self.valuation_id}")
            
        except Exception as e:
            self.conn.rollback()
            print(f"✗ Error saving inputs to database: {str(e)}")
            raise
    
    def display_shocks(self, shocks):
        """Display shock information"""
        print("\n" + "="*60)
        print("EQUITY SHOCKS BY CHARGE GROUP")
        print("="*60)
        for cat, shock_info in shocks.items():
            print(f"\n{cat}:")
            print(f"  Base Shock:          {shock_info['base_shock']*100:.2f}%")
            print(f"  Symmetric Adj:       {shock_info['symmetric_adjustment']*100:.2f}%")
            print(f"  Total Shock:         {shock_info['total_shock']*100:.2f}%")
        print("="*60 + "\n")
    
    def calculate_capital_requirements(self, allocation_df, shocks):
        """Calculate capital requirements by charge group"""
        
        # Group allocation by charge group and sum
        grouped = allocation_df.groupby('ChargeGroup')['CalculatedValue'].sum()
        
        results = {}
        
        for charge_group in ['Global', 'SA', 'Other']:
            value_pre = grouped.get(charge_group, 0)
            total_shock = shocks[charge_group]['total_shock']
            
            # Calculate post-shock value
            value_post = value_pre * (1 - total_shock)
            change_in_value = value_pre - value_post
            
            # Capital requirement (FSI 4.1 Section 6.10)
            capital_req = max(change_in_value, 0)
            
            results[charge_group] = {
                'total_shock': total_shock,
                'value_pre': value_pre,
                'value_post': value_post,
                'change_in_value': change_in_value,
                'capital_requirement': capital_req
            }
        
        print("\n" + "="*60)
        print("CAPITAL REQUIREMENTS BY CHARGE GROUP")
        print("="*60)
        for cat, res in results.items():
            print(f"\n{cat}:")
            print(f"  Value (Pre-Shock):   R {res['value_pre']:,.2f}")
            print(f"  Total Shock:         {res['total_shock']*100:.2f}%")
            print(f"  Change in Value:     R {res['change_in_value']:,.2f}")
            print(f"  Capital Requirement: R {res['capital_requirement']:,.2f}")
        print("="*60 + "\n")
        
        return results
    
    def aggregate_capital_requirements(self, cap_reqs):
        """Aggregate using correlation matrix"""
        
        categories = ['Global', 'SA', 'Other']
        cap_req_vector = np.array([cap_reqs[cat]['capital_requirement'] 
                                   for cat in categories])
        
        aggregated = np.sqrt(
            cap_req_vector @ self.correlation_matrix.values @ cap_req_vector
        )
        
        simple_sum = sum(cap_req_vector)
        correlation_benefit = simple_sum - aggregated
        
        print("\n" + "="*60)
        print("AGGREGATED EQUITY RISK CAPITAL REQUIREMENT")
        print("="*60)
        print(f"Global:              R {cap_reqs['Global']['capital_requirement']:,.2f}")
        print(f"SA:                  R {cap_reqs['SA']['capital_requirement']:,.2f}")
        print(f"Other:               R {cap_reqs['Other']['capital_requirement']:,.2f}")
        print(f"Simple Sum:          R {simple_sum:,.2f}")
        print(f"Correlation Benefit: R {correlation_benefit:,.2f}")
        print(f"Aggregated Total:    R {aggregated:,.2f}")
        print("="*60 + "\n")
        
        return {
            'global': cap_reqs['Global']['capital_requirement'],
            'sa': cap_reqs['SA']['capital_requirement'],
            'other': cap_reqs['Other']['capital_requirement'],
            'total': aggregated,
            'correlation_benefit': correlation_benefit
        }
    
    def save_results_to_database(self, cap_reqs, aggregated):
        """Save results to database"""
        cursor = self.conn.cursor()
        
        try:
            # Delete existing results
            cursor.execute("DELETE FROM Output_EquityRiskByGroup WHERE ValuationID = ?", 
                          (self.valuation_id,))
            cursor.execute("DELETE FROM Output_EquityRiskAggregated WHERE ValuationID = ?", 
                          (self.valuation_id,))
            
            # Insert results by group
            for charge_group, results in cap_reqs.items():
                cursor.execute("""
                    INSERT INTO Output_EquityRiskByGroup
                    (ValuationID, ChargeGroup, TotalShockApplied, ValuePreShock,
                     ValuePostShock, ChangeInValue, CapitalRequirement)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    self.valuation_id,
                    charge_group,
                    results['total_shock'],
                    results['value_pre'],
                    results['value_post'],
                    results['change_in_value'],
                    results['capital_requirement']
                ))
            

            
            self.conn.commit()
            print(f"✓ Saved results to database for Valuation ID: {self.valuation_id}")
            
        except Exception as e:
            self.conn.rollback()
            print(f"✗ Error saving results: {str(e)}")
            raise
    
    def export_results_to_excel(self, data, cap_reqs, aggregated):
        """Export results to Outputs sheet"""
        
        wb = load_workbook(self.excel_path)
        ws = wb['Outputs']
        
        # Update timestamp
        ws['B5'] = datetime.now()
        ws['B5'].number_format = 'yyyy-mm-dd hh:mm:ss'
        ws['D5'] = "Success"
        ws['D5'].font = Font(color="008000", bold=True)
        
        # Summary by charge group (rows 10-12)
        categories = ['Global', 'SA', 'Other']
        
        # Calculate allocations per group
        grouped_alloc = data['allocation'].groupby('ChargeGroup')['Allocation'].sum()
        
        for idx, cat in enumerate(categories, start=10):
            res = cap_reqs[cat]
            ws.cell(row=idx, column=1, value=cat)
            ws.cell(row=idx, column=2, value=grouped_alloc.get(cat, 0))
            ws.cell(row=idx, column=3, value=res['value_pre'])
            ws.cell(row=idx, column=4, value=res['total_shock'])
            ws.cell(row=idx, column=5, value=res['capital_requirement'])
        
        # Totals row (row 13)
        ws.cell(row=13, column=1, value="TOTAL")
        ws.cell(row=13, column=2, value=1.0)  # 100%
        ws.cell(row=13, column=3, value=sum(cap_reqs[c]['value_pre'] for c in categories))
        ws.cell(row=13, column=4, value="")
        ws.cell(row=13, column=5, value=aggregated['total'])
        
        # Aggregated results (rows 19-21)
        for idx, cat in enumerate(categories, start=19):
            ws.cell(row=idx, column=1, value=cat)
            ws.cell(row=idx, column=2, value=cap_reqs[cat]['capital_requirement'])
            ws.cell(row=idx, column=3, value=cap_reqs[cat]['capital_requirement']/aggregated['total'] if aggregated['total'] > 0 else 0)
        
        # Summary rows
        ws.cell(row=23, column=1, value="Simple Sum")
        ws.cell(row=23, column=2, value=sum(cap_reqs[c]['capital_requirement'] for c in categories))
        
        ws.cell(row=24, column=1, value="Correlation Benefit")
        ws.cell(row=24, column=2, value=aggregated['correlation_benefit'])
        
        ws.cell(row=25, column=1, value="Total Capital Requirement")
        ws.cell(row=25, column=2, value=aggregated['total'])
        
        # Save
        output_path = self.excel_path.replace('.xlsx', '_Results.xlsx')
        wb.save(output_path)
        print(f"✓ Results exported to: {output_path}")
        
        return output_path
    
    def log_calculation(self, run_start, run_end, status):
        """Log calculation to database"""
        cursor = self.conn.cursor()
        
        try:
            duration = int((run_end - run_start).total_seconds())
            
            cursor.execute("""
                INSERT INTO CalculationHistory
                (ValuationID, RunStartTime, RunEndTime, RunDuration, RunStatus,
                 InputFileLocation, RunBy)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                self.valuation_id,
                run_start,
                run_end,
                duration,
                status,
                self.excel_path,
                os.getlogin()
            ))
            
            self.conn.commit()
            
        except Exception as e:
            print(f"Warning: Could not log calculation: {str(e)}")
    
    def run_full_calculation(self):
        """Execute complete calculation"""
        
        run_start = datetime.now()
        
        try:
            print("\n" + "="*60)
            print("FSI 4.1 EQUITY RISK CALCULATION - V3")
            print("="*60 + "\n")
            
            # 1. Load data
            data = self.load_excel_data()
            
            # 2. Validate
            self.validate_allocation(data['allocation'])
            self.validate_shocks(data['shocks'])
            
            # 3. Connect to database
            self.connect_to_database()
            
            # 4. Save inputs
            self.save_inputs_to_database(data)
            
            # 5. Display shocks
            self.display_shocks(data['shocks'])
            
            # 6. Calculate capital requirements
            cap_reqs = self.calculate_capital_requirements(data['allocation'], data['shocks'])
            
            # 7. Aggregate
            aggregated = self.aggregate_capital_requirements(cap_reqs)
            
            # 8. Save results
          
            
            # 9. Export to Excel
            output_file = self.export_results_to_excel(data, cap_reqs, aggregated)
            
            # 10. Log
            run_end = datetime.now()
           
            
            print("\n" + "="*60)
            print("CALCULATION COMPLETE")
            print("="*60)
            print(f"Valuation ID:        {self.valuation_id}")
            print(f"Total Capital Req:   R {aggregated['total']:,.2f}")
            print(f"Output File:         {output_file}")
            print(f"Run Time:            {(run_end - run_start).total_seconds():.2f} seconds")
            print("="*60 + "\n")
            
            return {
                'valuation_id': self.valuation_id,
                'capital_requirements': cap_reqs,
                'aggregated': aggregated,
                'output_file': output_file
            }
            
        except Exception as e:
            run_end = datetime.now()
            if self.conn:
                self.log_calculation(run_start, run_end, 'Failed')
            print(f"\n✗ Calculation failed: {str(e)}")
            raise
        
        finally:
            if self.conn:
                self.conn.close()


# ============================================================================
# MAIN EXECUTION
# ============================================================================

if __name__ == "__main__":
    
    # Configuration
    DB_PATH = r"C:\Users\<USER>\OneDrive\Documents\SCR\Equity Risk\EquityRisk_Database.accdb"
    EXCEL_INPUT = r"C:\Users\<USER>\OneDrive\Documents\SCR\Equity Risk\EquityRisk_Input_Template_v3.xlsx"
    
    # Initialize calculator
    calculator = EquityRiskCalculator(DB_PATH, EXCEL_INPUT)
    
    # Run calculation
    try:
        results = calculator.run_full_calculation()
        print("\n✓ Calculation completed successfully!")
        
    except Exception as e:
        print(f"\n✗ Calculation failed with error:")
        print(f"  {str(e)}")
        import traceback
        traceback.print_exc()

#!/usr/bin/env python3
"""
Debug script to inspect Excel file contents
"""

from openpyxl import load_workbook

def inspect_excel_file():
    excel_path = r"C:\Users\<USER>\OneDrive\Documents\SCR\Equity Risk\EquityRisk_Input_Template_v3.xlsx"
    
    print("Inspecting Excel file...")
    print(f"File: {excel_path}")
    print("="*60)
    
    wb = load_workbook(excel_path, data_only=True)
    
    # List all worksheets
    print(f"Worksheets: {wb.sheetnames}")
    
    # Check if 'Inputs' sheet exists
    if 'Inputs' in wb.sheetnames:
        ws = wb['Inputs']
        print(f"\nInspecting 'Inputs' worksheet...")
    else:
        print(f"\n'Inputs' worksheet not found. Available sheets: {wb.sheetnames}")
        # Try the first sheet
        ws = wb[wb.sheetnames[0]]
        print(f"Using first sheet: {wb.sheetnames[0]}")
    
    print("\nInspecting key cells:")
    print("-" * 40)
    
    # Check the cells that the code is trying to read
    cells_to_check = [
        ('B5', 'valuation_id'),
        ('B6', 'valuation_date'), 
        ('B7', 'quarter'),
        ('B8', 'year'),
        ('B13', 'total_equity_value'),
        ('B14', 'total_assets'),
        ('B15', 'basic_own_funds'),
        ('B16', 'best_estimate_liabilities')
    ]
    
    for cell_ref, description in cells_to_check:
        cell_value = ws[cell_ref].value
        print(f"{cell_ref} ({description}): {repr(cell_value)} (type: {type(cell_value).__name__})")
    
    # Check some rows around row 22 for allocation data
    print(f"\nChecking allocation data around row 22:")
    print("-" * 40)
    for row in range(20, 30):
        a_val = ws[f'A{row}'].value
        b_val = ws[f'B{row}'].value
        c_val = ws[f'C{row}'].value
        d_val = ws[f'D{row}'].value
        if a_val or b_val or c_val or d_val:
            print(f"Row {row}: A={repr(a_val)}, B={repr(b_val)}, C={repr(c_val)}, D={repr(d_val)}")

    # Check shock data around rows 35-37
    print(f"\nChecking shock data around rows 35-37:")
    print("-" * 40)
    for row in range(33, 40):
        a_val = ws[f'A{row}'].value
        b_val = ws[f'B{row}'].value
        c_val = ws[f'C{row}'].value
        d_val = ws[f'D{row}'].value
        if a_val or b_val or c_val or d_val:
            print(f"Row {row}: A={repr(a_val)}, B={repr(b_val)}, C={repr(c_val)}, D={repr(d_val)}")

    wb.close()

if __name__ == "__main__":
    inspect_excel_file()
